#!/usr/bin/env python3
"""
Simple test for LEFT JOIN functionality.
"""

import pandas as pd
from sku_mapper import SkuMapper
import tempfile
import os

def test_left_join():
    """Test LEFT JOIN functionality with a small dataset."""
    
    print("Testing LEFT JOIN functionality...")
    
    # Create temporary test files
    temp_dir = tempfile.mkdtemp()
    a3_test_file = os.path.join(temp_dir, 'test_a3.xlsx')
    nielsen_test_file = os.path.join(temp_dir, 'test_nielsen.xlsx')
    
    try:
        # Sample A3 data - 3 records, only 2 should match
        a3_test_data = pd.DataFrame({
            'Fabricant': ['AB INBEV FRANCE', 'AB INBEV FRANCE', 'CARLSBERG GROUP'],
            'Marque': ['LEFFE', 'LEFFE', 'GRIMBERGEN'],
            'Marque fille': ['LEFFE BLONDE', 'LEFFE BLONDE', 'G<PERSON>MB<PERSON>GE<PERSON> BLONDE'],
            'Conditionnement': ['12 BTL 25 CL', '15 BTL 25 CL', '6 CAN 50 CL'],
            'Ean': ['1111111111111', '2222222222222', '5555555555555'],
            'SKU_': ['LEFFE12BTL25CL', 'LEFFE15BTL25CL', 'GRIM6CAN50CL']
        })
        
        # Sample Nielsen data - only 2 records, should match first 2 A3 records
        nielsen_test_data = pd.DataFrame({
            'FABRICANT': ['AB-INBEV', 'AB-INBEV'],
            'MARQUE': ['LEFFE', 'LEFFE'],
            'GAMME': ['LEFFE BLONDE', 'LEFFE BLONDE'],
            'CONDITIONNEMENT': ['BOITE', 'BOITE'],
            'NBR UNITE': ['X1', 'X1'],
            'CTN UNIT': ['250ML', '250ML'],
            'UPC': ['3333333333333', '4444444444444']
        })
        
        # Save A3 data
        with pd.ExcelWriter(a3_test_file, engine='openpyxl') as writer:
            a3_test_data.to_excel(writer, sheet_name='a3_distribution_march_2025', index=False)
        
        # Save Nielsen data with proper header structure
        # Create a DataFrame with 8 empty rows, then the data
        empty_rows = pd.DataFrame([[''] * len(nielsen_test_data.columns)] * 8)
        
        with pd.ExcelWriter(nielsen_test_file, engine='openpyxl') as writer:
            # Write empty rows first
            empty_rows.to_excel(writer, sheet_name='1-Table-1', index=False, header=False)
            
        # Append the actual data starting from row 9 (index 8)
        with pd.ExcelWriter(nielsen_test_file, engine='openpyxl', mode='a', if_sheet_exists='overlay') as writer:
            nielsen_test_data.to_excel(writer, sheet_name='1-Table-1', index=False, startrow=8)
        
        print(f"Created test files:")
        print(f"  A3: {a3_test_file}")
        print(f"  Nielsen: {nielsen_test_file}")
        
        # Test composite key generation
        print("\n=== Testing Composite Key Generation ===")
        mapper = SkuMapper(a3_test_file, nielsen_test_file)
        
        for idx, row in a3_test_data.iterrows():
            composite_key = mapper.create_composite_sku_key(row, 'a3')
            print(f"A3 Row {idx}: {row['SKU_']} -> {composite_key}")
        
        print()
        for idx, row in nielsen_test_data.iterrows():
            composite_key = mapper.create_composite_sku_key(row, 'nielsen')
            print(f"Nielsen Row {idx}: -> {composite_key}")
        
        print("\n=== Expected LEFT JOIN Results ===")
        print("- Total A3 records: 3")
        print("- Expected matches: 2 (LEFFE records)")
        print("- Expected unmatched: 1 (GRIMBERGEN record)")
        print("- Output should contain ALL 3 A3 records")
        
        # Test the full matching process
        print("\n=== Running Full LEFT JOIN Process ===")
        try:
            mapper.load_data()
            ean_matches = mapper.perform_ean_matching()
            all_matches = mapper.perform_composite_sku_matching(ean_matches)
            
            print(f"EAN matches: {sum(len(matches) for matches in ean_matches.values())}")
            print(f"Total matches: {sum(len(matches) for matches in all_matches.values())}")
            
            # This should create the LEFT JOIN output
            mapper.generate_left_join_output(all_matches)
            
            print("✅ LEFT JOIN output generated successfully!")
            print("Check the file: SKU_Mapping_A3_Nielsen_LEFT_JOIN_2025.xlsx")
            
        except Exception as e:
            print(f"❌ Error in LEFT JOIN process: {e}")
            import traceback
            traceback.print_exc()
    
    finally:
        # Cleanup
        try:
            if os.path.exists(a3_test_file):
                os.remove(a3_test_file)
            if os.path.exists(nielsen_test_file):
                os.remove(nielsen_test_file)
            os.rmdir(temp_dir)
        except:
            pass

if __name__ == "__main__":
    test_left_join()
