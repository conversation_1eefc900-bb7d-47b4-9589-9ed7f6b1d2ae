"""
Robust SKU Mapping Tool with Comprehensive Testing
=================================================

This is the production-ready version of the SKU mapper with:
- Comprehensive error handling and data validation
- Test-driven development approach
- Robust column mapping and data type handling
- Performance optimization for large datasets

Author: Augment Agent
Date: 2025-06-16
"""

import pandas as pd
import re
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import logging
import time
from dataclasses import dataclass

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


@dataclass
class MatchResult:
    """Data class to store matching results with full traceability."""
    a3_index: int
    nielsen_index: int
    a3_row: pd.Series
    nielsen_row: pd.Series
    match_type: str
    confidence_score: int
    matching_key: str
    match_notes: str
    year: str


class DataValidator:
    """Handles all data validation and quality checks."""
    
    @staticmethod
    def validate_a3_columns(df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        Validate that A3 dataframe has required columns.
        
        Args:
            df (pd.DataFrame): A3 dataframe to validate
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, missing_columns)
        """
        required_columns = ['Fabricant', 'Marque', 'Conditionnement', 'Ean', 'SKU_']
        missing_columns = [col for col in required_columns if col not in df.columns]
        return len(missing_columns) == 0, missing_columns
    
    @staticmethod
    def validate_nielsen_columns(df: pd.DataFrame) -> Tuple[bool, List[str]]:
        """
        Validate that Nielsen dataframe has required columns.
        
        Args:
            df (pd.DataFrame): Nielsen dataframe to validate
            
        Returns:
            Tuple[bool, List[str]]: (is_valid, missing_columns)
        """
        required_columns = ['FABRICANT', 'MARQUE', 'CONDITIONNEMENT', 'NBR UNITE', 'CTN UNIT', 'UPC']
        missing_columns = [col for col in required_columns if col not in df.columns]
        return len(missing_columns) == 0, missing_columns
    
    @staticmethod
    def clean_and_validate_data(df: pd.DataFrame, data_source: str) -> pd.DataFrame:
        """
        Clean and validate data quality.
        
        Args:
            df (pd.DataFrame): Dataframe to clean
            data_source (str): 'a3' or 'nielsen'
            
        Returns:
            pd.DataFrame: Cleaned dataframe
        """
        original_count = len(df)
        
        if data_source == 'a3':
            # Remove rows with missing critical fields
            df = df.dropna(subset=['Fabricant', 'Marque'])
            df = df[df['Fabricant'].str.strip() != '']
            
        elif data_source == 'nielsen':
            # Remove rows with missing critical fields
            df = df.dropna(subset=['FABRICANT', 'MARQUE', 'UPC'])
            df = df[
                (df['FABRICANT'].str.strip() != '') &
                (df['FABRICANT'] != 'FABRICANT') &
                (df['FABRICANT'] != 'Markets') &
                (df['UPC'].str.strip() != '')
            ]
        
        cleaned_count = len(df)
        logger.info(f"Data cleaning: {original_count} -> {cleaned_count} rows ({data_source})")
        
        return df


class RobustSkuMapper:
    """
    Production-ready SKU mapper with comprehensive error handling and testing.
    
    Features:
    - Robust data loading with validation
    - Comprehensive error handling
    - Performance monitoring
    - Test-driven development support
    """
    
    def __init__(self, a3_file_path: str, nielsen_file_path: str, test_mode: bool = False):
        """
        Initialize the robust SKU mapper.
        
        Args:
            a3_file_path (str): Path to A3 Excel file
            nielsen_file_path (str): Path to Nielsen Excel file
            test_mode (bool): If True, enables additional logging and validation
        """
        self.a3_file_path = Path(a3_file_path)
        self.nielsen_file_path = Path(nielsen_file_path)
        self.test_mode = test_mode
        self.a3_data = None
        self.nielsen_data_by_year = {}
        self.validator = DataValidator()
        
        # Performance tracking
        self.performance_metrics = {
            'load_time': 0,
            'ean_match_time': 0,
            'composite_match_time': 0,
            'output_time': 0
        }
        
        # Normalization mappings with comprehensive coverage
        self.manufacturer_mapping = {
            'AB INBEV FRANCE': 'AB-INBEV',
            'CARLSBERG GROUP': 'CARLSBERG',
            'HEINEKEN ENTREPRISE': 'HEINEKEN',
            'BRASSERIE GOUDALE': 'GOUDALE',
            'I.B.B.': 'IBB',
            'BRASSERIES KRONENBOURG': 'KRONENBOURG'
        }
        
        self.brand_mapping = {
            'ABBAYE DE LEFFE': 'LEFFE',
            'LA GOUDALE': 'GOUDALE',
            'BRASSERIE DU PELICAN': 'PELICAN',
            'SECRET DES MOINES': 'SECRET DES MOINES'
        }
        
        # Validation patterns
        self.ean_pattern = re.compile(r'^\d{8,14}$')  # EAN should be 8-14 digits
        self.volume_pattern = re.compile(r'(\d+)\s*(CL|ML)', re.IGNORECASE)
        
    def validate_file_paths(self) -> None:
        """Validate that input files exist and are accessible."""
        if not self.a3_file_path.exists():
            raise FileNotFoundError(f"A3 file not found: {self.a3_file_path}")
        
        if not self.nielsen_file_path.exists():
            raise FileNotFoundError(f"Nielsen file not found: {self.nielsen_file_path}")
        
        logger.info("File paths validated successfully")
    
    def load_a3_data(self) -> None:
        """Load and validate A3 data with comprehensive error handling."""
        start_time = time.time()
        
        try:
            logger.info("Loading A3 data...")
            
            # Load data
            self.a3_data = pd.read_excel(
                self.a3_file_path, 
                sheet_name='a3_distribution_march_2025'
            )
            
            logger.info(f"Loaded {len(self.a3_data)} rows from A3 data")
            
            # Validate columns
            is_valid, missing_cols = self.validator.validate_a3_columns(self.a3_data)
            if not is_valid:
                raise ValueError(f"A3 data missing required columns: {missing_cols}")
            
            # Clean and validate data
            self.a3_data = self.validator.clean_and_validate_data(self.a3_data, 'a3')
            
            if len(self.a3_data) == 0:
                raise ValueError("No valid A3 data found after cleaning")
            
            # Data type optimization for performance
            self.a3_data['Fabricant'] = self.a3_data['Fabricant'].astype('string')
            self.a3_data['Marque'] = self.a3_data['Marque'].astype('string')
            self.a3_data['Ean'] = self.a3_data['Ean'].astype('string')
            
            self.performance_metrics['load_time'] += time.time() - start_time
            logger.info(f"A3 data loaded and validated successfully: {len(self.a3_data)} rows")
            
        except Exception as e:
            logger.error(f"Error loading A3 data: {e}")
            raise
    
    def load_nielsen_data(self) -> None:
        """Load and validate Nielsen data with comprehensive error handling."""
        start_time = time.time()
        
        try:
            logger.info("Loading Nielsen data...")
            
            nielsen_sheets = {
                '2022': '1-Table-1',
                '2023': '2-Table-1', 
                '2024': '3-Table-1',
                '2024_2': '4-Table-1',
                '2025': '5-Table-1'
            }
            
            for year, sheet_name in nielsen_sheets.items():
                try:
                    logger.info(f"Loading Nielsen {year} data from sheet: {sheet_name}")
                    
                    # Load with proper header handling
                    df = pd.read_excel(
                        self.nielsen_file_path, 
                        sheet_name=sheet_name, 
                        header=8  # Headers are in row 9 (index 8)
                    )
                    
                    # Clean column names
                    df.columns = [
                        str(col).strip() if pd.notna(col) else f'Col_{i}' 
                        for i, col in enumerate(df.columns)
                    ]
                    
                    # Validate columns
                    is_valid, missing_cols = self.validator.validate_nielsen_columns(df)
                    if not is_valid:
                        logger.warning(f"Nielsen {year} missing columns: {missing_cols}")
                        logger.info(f"Available columns: {list(df.columns)}")
                        continue
                    
                    # Clean and validate data
                    df = self.validator.clean_and_validate_data(df, 'nielsen')
                    
                    if len(df) == 0:
                        logger.warning(f"No valid data found for Nielsen {year}")
                        continue
                    
                    # Data type optimization
                    df['FABRICANT'] = df['FABRICANT'].astype('string')
                    df['MARQUE'] = df['MARQUE'].astype('string')
                    df['UPC'] = df['UPC'].astype('string')
                    
                    self.nielsen_data_by_year[year] = df
                    logger.info(f"Loaded Nielsen {year}: {len(df)} valid rows")
                    
                except Exception as e:
                    logger.error(f"Error loading Nielsen {year} data: {e}")
                    continue
            
            if not self.nielsen_data_by_year:
                raise ValueError("No valid Nielsen data loaded from any year")
            
            self.performance_metrics['load_time'] += time.time() - start_time
            total_nielsen_rows = sum(len(df) for df in self.nielsen_data_by_year.values())
            logger.info(f"Nielsen data loaded successfully: {total_nielsen_rows} total rows across {len(self.nielsen_data_by_year)} years")
            
        except Exception as e:
            logger.error(f"Error loading Nielsen data: {e}")
            raise

    def normalize_manufacturer(self, manufacturer: str) -> str:
        """Normalize manufacturer names with error handling."""
        if pd.isna(manufacturer) or manufacturer == '':
            return ''

        try:
            manufacturer = str(manufacturer).strip().upper()
            return self.manufacturer_mapping.get(manufacturer, manufacturer)
        except Exception as e:
            logger.warning(f"Error normalizing manufacturer '{manufacturer}': {e}")
            return str(manufacturer) if manufacturer else ''

    def normalize_brand(self, brand: str) -> str:
        """Normalize brand names with error handling."""
        if pd.isna(brand) or brand == '':
            return ''

        try:
            brand = str(brand).strip().upper()
            return self.brand_mapping.get(brand, brand)
        except Exception as e:
            logger.warning(f"Error normalizing brand '{brand}': {e}")
            return str(brand) if brand else ''

    def normalize_volume(self, volume_text: str) -> str:
        """Normalize volume measurements with error handling."""
        if pd.isna(volume_text) or volume_text == '':
            return ''

        try:
            volume_text = str(volume_text).upper()
            matches = self.volume_pattern.findall(volume_text)
            normalized_volumes = []

            for amount, unit in matches:
                if unit == 'CL':
                    ml_amount = int(amount) * 10
                    normalized_volumes.append(f"{ml_amount}ML")
                else:
                    normalized_volumes.append(f"{amount}ML")

            return ' '.join(normalized_volumes) if normalized_volumes else volume_text

        except Exception as e:
            logger.warning(f"Error normalizing volume '{volume_text}': {e}")
            return str(volume_text) if volume_text else ''

    def extract_and_validate_eans(self, ean_string: str) -> List[str]:
        """Extract and validate EAN codes with comprehensive error handling."""
        if pd.isna(ean_string) or ean_string == '':
            return []

        try:
            ean_string = str(ean_string).strip()
            if not ean_string:
                return []

            # Handle scientific notation (e.g., "5.41E+12")
            if 'E+' in ean_string.upper():
                try:
                    # Convert scientific notation to regular number
                    ean_float = float(ean_string)
                    ean_string = f"{ean_float:.0f}"
                except ValueError:
                    logger.warning(f"Could not convert scientific notation EAN: {ean_string}")
                    return []

            # Split by underscore for multiple EANs
            eans = [ean.strip() for ean in ean_string.split('_') if ean.strip()]

            # Validate and clean each EAN
            valid_eans = []
            for ean in eans:
                # Remove non-numeric characters
                clean_ean = re.sub(r'[^\d]', '', ean)

                # Validate EAN format
                if self.ean_pattern.match(clean_ean):
                    valid_eans.append(clean_ean)
                elif len(clean_ean) >= 8:  # Accept longer codes but warn
                    logger.warning(f"Unusual EAN length: {clean_ean}")
                    valid_eans.append(clean_ean)

            return valid_eans

        except Exception as e:
            logger.warning(f"Error extracting EANs from '{ean_string}': {e}")
            return []

    def create_composite_sku_key(self, row: pd.Series, data_source: str) -> str:
        """Create composite SKU key with comprehensive error handling."""
        try:
            if data_source == 'a3':
                manufacturer = self.normalize_manufacturer(row.get('Fabricant', ''))
                brand = self.normalize_brand(row.get('Marque', ''))
                sub_brand = self.normalize_brand(row.get('Marque fille', ''))
                pack_info = str(row.get('Conditionnement', '')).upper()

            elif data_source == 'nielsen':
                manufacturer = self.normalize_manufacturer(row.get('FABRICANT', ''))
                brand = self.normalize_brand(row.get('MARQUE', ''))
                sub_brand = self.normalize_brand(row.get('GAMME', ''))

                # Construct pack info from Nielsen fields
                container = str(row.get('CONDITIONNEMENT', '')).upper()
                count = str(row.get('NBR UNITE', '')).replace('X', '').strip()
                volume = str(row.get('CTN UNIT', '')).upper()
                pack_info = f"{count} {container} {volume}".strip()

            else:
                raise ValueError(f"Invalid data_source: {data_source}")

            # Normalize pack info
            pack_info = self.normalize_volume(pack_info)

            # Create composite key
            key_parts = [manufacturer, brand, sub_brand, pack_info]
            composite_key = '|'.join([part.strip() for part in key_parts if part.strip()])

            return composite_key

        except Exception as e:
            logger.warning(f"Error creating composite key for {data_source} row: {e}")
            return ''
