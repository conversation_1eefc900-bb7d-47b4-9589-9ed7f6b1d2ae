#!/usr/bin/env python3
"""
Test script to verify the pack size fix in SKU mapping.
"""

import pandas as pd
from sku_mapper import SkuMapper
import tempfile
import os

def test_pack_size_matching():
    """Test that pack sizes are handled correctly in composite matching."""
    
    print("Testing pack size matching fix...")
    
    # Create temporary test files
    temp_dir = tempfile.mkdtemp()
    a3_test_file = os.path.join(temp_dir, 'test_a3.xlsx')
    nielsen_test_file = os.path.join(temp_dir, 'test_nielsen.xlsx')
    
    try:
        # Sample A3 data with different scenarios for LEFT JOIN testing
        a3_test_data = pd.DataFrame({
            'Fabricant': ['AB INBEV FRANCE', 'AB INBEV FRANCE', 'CARLSBERG GROUP'],
            'Marque': ['LEFFE', 'LEFFE', 'GRIMBERGEN'],
            'Marque fille': ['LEFFE BLONDE', 'LEFFE BLONDE', 'G<PERSON><PERSON><PERSON>GE<PERSON> BLONDE'],
            'Conditionnement': ['12 BTL 25 CL', '15 BTL 25 CL', '6 CAN 50 CL'],
            'Ean': ['1111111111111', '2222222222222', '5555555555555'],  # Third one won't match
            'SKU_': ['LEFFE12BTL25CL', 'LEFFE15BTL25CL', 'GRIM6CAN50CL']
        })

        # Sample Nielsen data - only matches first two A3 records, third A3 should be unmatched
        nielsen_test_data_2022 = pd.DataFrame({
            'FABRICANT': ['AB-INBEV', 'AB-INBEV'],
            'MARQUE': ['LEFFE', 'LEFFE'],
            'GAMME': ['LEFFE BLONDE', 'LEFFE BLONDE'],
            'CONDITIONNEMENT': ['BOITE', 'BOITE'],
            'NBR UNITE': ['X1', 'X1'],
            'CTN UNIT': ['250ML', '250ML'],
            'UPC': ['3333333333333', '4444444444444']  # Different UPCs, will use composite matching
        })
        
        # Save test data to Excel files
        with pd.ExcelWriter(a3_test_file, engine='openpyxl') as writer:
            a3_test_data.to_excel(writer, sheet_name='a3_distribution_march_2025', index=False)
        
        # Create Nielsen file with proper structure
        with pd.ExcelWriter(nielsen_test_file, engine='openpyxl') as writer:
            # Add empty rows before data (header is at row 9)
            empty_df = pd.DataFrame([[''] * len(nielsen_test_data_2022.columns)] * 8)
            empty_df.to_excel(writer, sheet_name='1-Table-1', index=False, header=False)
            
            # Add the actual data starting from row 9
            with pd.ExcelWriter(nielsen_test_file, engine='openpyxl', mode='a', if_sheet_exists='overlay') as writer2:
                nielsen_test_data_2022.to_excel(writer2, sheet_name='1-Table-1', index=False, startrow=8)
        
        # Initialize mapper
        mapper = SkuMapper(a3_test_file, nielsen_test_file)
        
        # Test composite key generation
        print("\n=== Testing Composite Key Generation ===")
        
        # Test A3 composite keys
        for idx, row in a3_test_data.iterrows():
            composite_key = mapper.create_composite_sku_key(row, 'a3')
            print(f"A3 Row {idx}: {row['Conditionnement']} -> {composite_key}")
            
            # Test pack component extraction
            pack_components = mapper.extract_pack_components(row['Conditionnement'], 'a3')
            print(f"  Pack components: {pack_components}")
        
        print()
        
        # Test Nielsen composite keys
        for idx, row in nielsen_test_data_2022.iterrows():
            composite_key = mapper.create_composite_sku_key(row, 'nielsen')
            print(f"Nielsen Row {idx}: {row['CONDITIONNEMENT']} {row['NBR UNITE']} {row['CTN UNIT']} -> {composite_key}")
        
        print("\n=== Expected Behavior ===")
        print("First two A3 records should generate similar composite keys (ignoring pack count):")
        print("- AB-INBEV|LEFFE|LEFFE BLONDE|BOUTEILLE 250ML")
        print("Both Nielsen records should generate:")
        print("- AB-INBEV|LEFFE|LEFFE BLONDE|BOITE 250ML")
        print("Third A3 record (GRIMBERGEN) should remain UNMATCHED in LEFT JOIN")
        print("These should match based on brand/manufacturer/volume similarity")
        
        # Test the full matching process on this small dataset
        print("\n=== Testing Full Matching Process ===")
        try:
            mapper.load_data()
            ean_matches = mapper.perform_ean_matching()
            all_matches = mapper.perform_composite_sku_matching(ean_matches)
            
            print(f"EAN matches found: {sum(len(matches) for matches in ean_matches.values())}")
            print(f"Total matches found: {sum(len(matches) for matches in all_matches.values())}")
            
            # Show match details
            for year, matches in all_matches.items():
                print(f"\nYear {year} matches:")
                for match in matches:
                    print(f"  {match['match_type']}: {match['match_notes']}")

            print(f"\n=== LEFT JOIN Test Results ===")
            print(f"Total A3 records: {len(a3_test_data)}")
            print(f"Expected: ALL 3 A3 records in output (2 matched + 1 unmatched)")
            print(f"This tests the LEFT JOIN functionality!")
                    
        except Exception as e:
            print(f"Error in matching process: {e}")
            import traceback
            traceback.print_exc()
    
    finally:
        # Cleanup
        try:
            if os.path.exists(a3_test_file):
                os.remove(a3_test_file)
            if os.path.exists(nielsen_test_file):
                os.remove(nielsen_test_file)
            os.rmdir(temp_dir)
        except:
            pass

if __name__ == "__main__":
    test_pack_size_matching()
