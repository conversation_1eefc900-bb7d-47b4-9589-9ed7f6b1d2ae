"""
Test Data Creator for SKU Mapping Tool
=====================================

This module creates small test datasets from the full A3 and Nielsen data
to enable rapid testing and debugging of the SKU mapping logic.

Author: Augment Agent
Date: 2025-06-16
"""

import pandas as pd
import logging
from pathlib import Path
from typing import Dict, List

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestDataCreator:
    """
    Creates representative test datasets from the full A3 and Nielsen data.
    
    This class extracts small subsets that include:
    - Various manufacturers and brands
    - Different pack formats and sizes
    - Multiple EAN formats
    - Edge cases (missing data, special characters)
    """
    
    def __init__(self, a3_file_path: str, nielsen_file_path: str):
        """
        Initialize the test data creator.
        
        Args:
            a3_file_path (str): Path to full A3 Excel file
            nielsen_file_path (str): Path to full Nielsen Excel file
        """
        self.a3_file_path = Path(a3_file_path)
        self.nielsen_file_path = Path(nielsen_file_path)
        
    def create_a3_test_data(self, sample_size: int = 100) -> pd.DataFrame:
        """
        Create a representative test dataset from A3 data.
        
        Args:
            sample_size (int): Number of rows to extract
            
        Returns:
            pd.DataFrame: Test dataset with diverse examples
        """
        logger.info(f"Creating A3 test data with {sample_size} samples...")
        
        try:
            # Load full A3 data
            df = pd.read_excel(self.a3_file_path, sheet_name='a3_distribution_march_2025')
            logger.info(f"Loaded {len(df)} rows from A3 data")
            
            # Create stratified sample to ensure diversity
            test_samples = []
            
            # Sample by manufacturer to ensure variety
            manufacturers = df['Fabricant'].value_counts().head(5).index.tolist()
            samples_per_manufacturer = sample_size // len(manufacturers)
            
            for manufacturer in manufacturers:
                manufacturer_data = df[df['Fabricant'] == manufacturer]
                if len(manufacturer_data) > 0:
                    # Sample different brands within manufacturer
                    sample = manufacturer_data.sample(
                        n=min(samples_per_manufacturer, len(manufacturer_data)),
                        random_state=42
                    )
                    test_samples.append(sample)
            
            # Combine all samples
            test_df = pd.concat(test_samples, ignore_index=True)
            
            # Add some edge cases manually if they exist
            edge_cases = []
            
            # Find rows with multiple EANs
            multi_ean_rows = df[df['Ean'].str.contains('_', na=False)]
            if len(multi_ean_rows) > 0:
                edge_cases.append(multi_ean_rows.sample(n=min(5, len(multi_ean_rows)), random_state=42))
            
            # Find rows with missing EANs
            missing_ean_rows = df[df['Ean'].isna() | (df['Ean'] == '')]
            if len(missing_ean_rows) > 0:
                edge_cases.append(missing_ean_rows.sample(n=min(3, len(missing_ean_rows)), random_state=42))
            
            # Find rows with scientific notation EANs
            sci_notation_rows = df[df['Ean'].str.contains('E+', na=False)]
            if len(sci_notation_rows) > 0:
                edge_cases.append(sci_notation_rows.sample(n=min(3, len(sci_notation_rows)), random_state=42))
            
            # Add edge cases to test data
            if edge_cases:
                edge_df = pd.concat(edge_cases, ignore_index=True)
                test_df = pd.concat([test_df, edge_df], ignore_index=True).drop_duplicates()
            
            # Ensure we don't exceed sample_size
            if len(test_df) > sample_size:
                test_df = test_df.sample(n=sample_size, random_state=42)
            
            logger.info(f"Created A3 test dataset with {len(test_df)} rows")
            return test_df
            
        except Exception as e:
            logger.error(f"Error creating A3 test data: {e}")
            raise
    
    def create_nielsen_test_data(self, sample_size: int = 100) -> Dict[str, pd.DataFrame]:
        """
        Create representative test datasets from Nielsen data for each year.
        
        Args:
            sample_size (int): Number of rows to extract per year
            
        Returns:
            Dict[str, pd.DataFrame]: Test datasets by year
        """
        logger.info(f"Creating Nielsen test data with {sample_size} samples per year...")
        
        nielsen_sheets = {
            '2022': '1-Table-1',
            '2023': '2-Table-1', 
            '2024': '3-Table-1',
            '2024_2': '4-Table-1',
            '2025': '5-Table-1'
        }
        
        test_data_by_year = {}
        
        for year, sheet_name in nielsen_sheets.items():
            try:
                logger.info(f"Processing Nielsen {year} data...")
                
                # Load data with proper header handling
                df = pd.read_excel(self.nielsen_file_path, sheet_name=sheet_name, header=8)
                
                # Clean column names
                df.columns = [str(col).strip() if pd.notna(col) else f'Col_{i}' for i, col in enumerate(df.columns)]
                
                # Filter valid data rows
                df = df[
                    df['FABRICANT'].notna() & 
                    (df['FABRICANT'] != '') & 
                    (df['FABRICANT'] != 'FABRICANT') &
                    (df['FABRICANT'] != 'Markets') &
                    df['UPC'].notna() &
                    (df['UPC'] != '')
                ]
                
                if len(df) == 0:
                    logger.warning(f"No valid data found for year {year}")
                    continue
                
                # Create stratified sample
                test_samples = []
                
                # Sample by manufacturer
                manufacturers = df['FABRICANT'].value_counts().head(3).index.tolist()
                samples_per_manufacturer = sample_size // len(manufacturers) if manufacturers else sample_size
                
                for manufacturer in manufacturers:
                    manufacturer_data = df[df['FABRICANT'] == manufacturer]
                    if len(manufacturer_data) > 0:
                        sample = manufacturer_data.sample(
                            n=min(samples_per_manufacturer, len(manufacturer_data)),
                            random_state=42
                        )
                        test_samples.append(sample)
                
                # If no manufacturers found, take random sample
                if not test_samples:
                    test_samples.append(df.sample(n=min(sample_size, len(df)), random_state=42))
                
                test_df = pd.concat(test_samples, ignore_index=True)
                
                # Ensure we don't exceed sample_size
                if len(test_df) > sample_size:
                    test_df = test_df.sample(n=sample_size, random_state=42)
                
                test_data_by_year[year] = test_df
                logger.info(f"Created Nielsen {year} test dataset with {len(test_df)} rows")
                
            except Exception as e:
                logger.error(f"Error creating Nielsen test data for year {year}: {e}")
                continue
        
        return test_data_by_year
    
    def save_test_datasets(self, a3_sample_size: int = 100, nielsen_sample_size: int = 50) -> None:
        """
        Create and save test datasets to Excel files.
        
        Args:
            a3_sample_size (int): Size of A3 test dataset
            nielsen_sample_size (int): Size of Nielsen test dataset per year
        """
        try:
            # Create A3 test data
            a3_test_data = self.create_a3_test_data(a3_sample_size)
            
            # Save A3 test data
            a3_test_file = Path("test_a3_data.xlsx")
            with pd.ExcelWriter(a3_test_file, engine='openpyxl') as writer:
                a3_test_data.to_excel(writer, sheet_name='a3_distribution_march_2025', index=False)
            
            logger.info(f"Saved A3 test data to: {a3_test_file}")
            
            # Create Nielsen test data
            nielsen_test_data = self.create_nielsen_test_data(nielsen_sample_size)
            
            # Save Nielsen test data
            nielsen_test_file = Path("test_nielsen_data.xlsx")
            with pd.ExcelWriter(nielsen_test_file, engine='openpyxl') as writer:
                # Create index sheet
                index_data = []
                for year, df in nielsen_test_data.items():
                    sheet_name = f"{year.replace('_', '-')}-Table-1"
                    index_data.append({
                        'Worksheet Tab': sheet_name,
                        'Report Title': year,
                        'Rows': len(df)
                    })
                    df.to_excel(writer, sheet_name=sheet_name, index=False, startrow=8)
                    
                    # Add header info to match original format
                    worksheet = writer.sheets[sheet_name]
                    worksheet.cell(row=9, column=1, value='Markets')
                    worksheet.cell(row=9, column=2, value='Periods')
                    # ... (headers are already in the data)
                
                # Save index
                index_df = pd.DataFrame(index_data)
                index_df.to_excel(writer, sheet_name='Index', index=False)
            
            logger.info(f"Saved Nielsen test data to: {nielsen_test_file}")
            
            # Print summary
            print("\n" + "="*60)
            print("TEST DATA CREATION SUMMARY")
            print("="*60)
            print(f"A3 Test Data: {len(a3_test_data)} rows")
            print(f"Nielsen Test Data:")
            for year, df in nielsen_test_data.items():
                print(f"  - {year}: {len(df)} rows")
            print(f"Total Nielsen Test Rows: {sum(len(df) for df in nielsen_test_data.values())}")
            print("="*60)
            
        except Exception as e:
            logger.error(f"Error saving test datasets: {e}")
            raise


def main():
    """Create test datasets from the full data files."""
    creator = TestDataCreator(
        a3_file_path="a3_distribution_march_2025_Latest.xlsx",
        nielsen_file_path="CARREFOUR (incl. Drive) Data Pull 3.xlsx"
    )
    
    creator.save_test_datasets(a3_sample_size=100, nielsen_sample_size=50)


if __name__ == "__main__":
    main()
